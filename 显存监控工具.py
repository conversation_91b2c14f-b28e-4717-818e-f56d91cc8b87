"""
GPU显存监控和诊断工具
"""

import torch
import psutil
import time
from functools import wraps

class GPUMemoryMonitor:
    """GPU显存监控类"""
    
    def __init__(self, device='cuda:0'):
        self.device = device
        self.peak_memory = 0
        self.checkpoints = []
    
    def get_memory_info(self):
        """获取当前显存信息"""
        if torch.cuda.is_available():
            allocated = torch.cuda.memory_allocated(self.device) / 1024**3  # GB
            reserved = torch.cuda.memory_reserved(self.device) / 1024**3   # GB
            max_allocated = torch.cuda.max_memory_allocated(self.device) / 1024**3  # GB
            
            return {
                'allocated': allocated,
                'reserved': reserved, 
                'max_allocated': max_allocated,
                'free': torch.cuda.get_device_properties(self.device).total_memory / 1024**3 - reserved
            }
        return None
    
    def print_memory_info(self, tag=""):
        """打印显存信息"""
        info = self.get_memory_info()
        if info:
            print(f"[{tag}] GPU Memory:")
            print(f"  Allocated: {info['allocated']:.2f}GB")
            print(f"  Reserved:  {info['reserved']:.2f}GB") 
            print(f"  Max Used:  {info['max_allocated']:.2f}GB")
            print(f"  Free:      {info['free']:.2f}GB")
            print("-" * 40)
    
    def checkpoint(self, name):
        """记录检查点"""
        info = self.get_memory_info()
        if info:
            self.checkpoints.append({
                'name': name,
                'time': time.time(),
                'memory': info.copy()
            })
            self.peak_memory = max(self.peak_memory, info['allocated'])
    
    def memory_summary(self):
        """显存使用总结"""
        print("=== GPU Memory Usage Summary ===")
        print(f"Peak Memory Usage: {self.peak_memory:.2f}GB")
        print("\nCheckpoints:")
        for cp in self.checkpoints:
            print(f"  {cp['name']}: {cp['memory']['allocated']:.2f}GB")
        print("=" * 35)

def memory_profiler(func):
    """装饰器：监控函数的显存使用"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        monitor = GPUMemoryMonitor()
        monitor.print_memory_info(f"Before {func.__name__}")
        
        result = func(*args, **kwargs)
        
        monitor.print_memory_info(f"After {func.__name__}")
        return result
    return wrapper

def diagnose_memory_leak():
    """诊断显存泄漏"""
    print("=== GPU Memory Leak Diagnosis ===")
    
    monitor = GPUMemoryMonitor()
    monitor.checkpoint("Initial")
    
    # 模拟一些操作
    print("Creating test tensors...")
    tensors = []
    for i in range(10):
        # 创建一些张量
        x = torch.randn(1000, 1000).cuda()
        tensors.append(x)
        monitor.checkpoint(f"Tensor {i+1}")
        
        if i % 3 == 0:
            monitor.print_memory_info(f"Step {i+1}")
    
    print("Cleaning up...")
    del tensors
    torch.cuda.empty_cache()
    monitor.checkpoint("After cleanup")
    monitor.print_memory_info("Final")
    
    monitor.memory_summary()

def check_transformer_memory(seq_len, d_model, num_heads):
    """检查Transformer显存需求"""
    print(f"=== Transformer Memory Analysis ===")
    print(f"Sequence Length: {seq_len}")
    print(f"Model Dimension: {d_model}")
    print(f"Number of Heads: {num_heads}")
    
    # 估算注意力矩阵大小
    attention_matrix_size = seq_len * seq_len * num_heads * 4 / 1024**3  # 4 bytes per float32
    print(f"Attention Matrix Size: {attention_matrix_size:.2f}GB")
    
    # 估算总显存需求
    model_params = d_model * d_model * 4 * 4 / 1024**3  # 粗略估算
    total_estimate = attention_matrix_size + model_params
    print(f"Estimated Total Memory: {total_estimate:.2f}GB")
    
    if total_estimate > 10:
        print("⚠️  WARNING: Memory usage may be too high!")
        print("Suggestions:")
        print(f"  - Reduce sequence length (current: {seq_len})")
        print(f"  - Reduce number of heads (current: {num_heads})")
        print(f"  - Use gradient checkpointing")
    
    return total_estimate

def optimize_memory_usage():
    """显存优化建议"""
    print("=== Memory Optimization Tips ===")
    
    tips = [
        "1. 使用 torch.cuda.empty_cache() 清理缓存",
        "2. 在训练循环中使用 optimizer.zero_grad()",
        "3. 避免在循环中累积张量",
        "4. 使用 .item() 获取标量值而不是保持张量",
        "5. 使用混合精度训练 (torch.cuda.amp)",
        "6. 使用梯度检查点 (gradient checkpointing)",
        "7. 减小batch size或序列长度",
        "8. 使用 del 手动删除大张量"
    ]
    
    for tip in tips:
        print(tip)
    
    print("\n=== Code Examples ===")
    
    print("✅ Good practice:")
    print("""
for epoch in range(num_epochs):
    for batch in dataloader:
        optimizer.zero_grad()
        
        loss = model(batch)
        loss.backward()
        optimizer.step()
        
        # 清理
        del loss
        torch.cuda.empty_cache()  # 可选，但有时有用
""")
    
    print("❌ Bad practice:")
    print("""
losses = []
for epoch in range(num_epochs):
    for batch in dataloader:
        loss = model(batch)
        losses.append(loss)  # 保持计算图！
        loss.backward()
        # 忘记 optimizer.zero_grad()
""")

def main():
    """主函数"""
    print("GPU Memory Diagnostic Tool")
    print("=" * 50)
    
    # 检查GPU可用性
    if not torch.cuda.is_available():
        print("CUDA is not available!")
        return
    
    # 显示GPU信息
    device = torch.cuda.current_device()
    gpu_name = torch.cuda.get_device_name(device)
    total_memory = torch.cuda.get_device_properties(device).total_memory / 1024**3
    
    print(f"GPU: {gpu_name}")
    print(f"Total Memory: {total_memory:.2f}GB")
    print()
    
    # 当前显存状态
    monitor = GPUMemoryMonitor()
    monitor.print_memory_info("Current Status")
    
    # 分析Transformer显存需求（基于你的配置）
    print("Analyzing your Transformer configuration...")
    # 假设节点数，你需要根据实际数据集调整
    estimated_nodes = 50000  # 根据你的数据集调整这个数字
    check_transformer_memory(estimated_nodes, 512, 8)
    
    print()
    optimize_memory_usage()

if __name__ == "__main__":
    main()
