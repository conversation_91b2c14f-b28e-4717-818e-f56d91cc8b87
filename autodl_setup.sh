#!/bin/bash
# AutoDL环境配置脚本

echo "=== AutoDL环境配置开始 ==="

# 1. 更新系统包
echo "更新系统包..."
apt update

# 2. 安装必要的系统依赖
echo "安装系统依赖..."
apt install -y git vim htop tree

# 3. 检查GPU状态
echo "检查GPU状态..."
nvidia-smi

# 4. 检查CUDA版本
echo "检查CUDA版本..."
nvcc --version

# 5. 检查PyTorch安装
echo "检查PyTorch安装..."
python -c "import torch; print(f'PyTorch版本: {torch.__version__}'); print(f'CUDA可用: {torch.cuda.is_available()}'); print(f'CUDA版本: {torch.version.cuda}')"

# 6. 安装额外的Python包
echo "安装Python依赖包..."
pip install -i https://pypi.tuna.tsinghua.edu.cn/simple/ \
    dgl-cu113 \
    networkx \
    scikit-learn \
    pandas \
    matplotlib \
    seaborn \
    tqdm \
    tensorboard \
    wandb

# 7. 创建工作目录
echo "创建工作目录..."
mkdir -p /root/experiments
mkdir -p /root/experiments/logs
mkdir -p /root/experiments/results
mkdir -p /root/experiments/configs

# 8. 设置环境变量
echo "设置环境变量..."
echo 'export CUDA_VISIBLE_DEVICES=0' >> ~/.bashrc
echo 'export PYTHONPATH=/root/experiments:$PYTHONPATH' >> ~/.bashrc

# 9. 创建快速监控脚本
cat > /root/monitor_gpu.sh << 'EOF'
#!/bin/bash
while true; do
    clear
    echo "=== GPU监控 $(date) ==="
    nvidia-smi
    echo ""
    echo "=== 内存使用 ==="
    free -h
    echo ""
    echo "=== 磁盘使用 ==="
    df -h
    sleep 5
done
EOF

chmod +x /root/monitor_gpu.sh

# 10. 创建实验管理脚本
cat > /root/experiments/run_experiment.py << 'EOF'
#!/usr/bin/env python3
"""
AutoDL实验运行脚本
"""
import os
import sys
import time
import json
import subprocess
from datetime import datetime

def run_single_experiment(config, exp_name):
    """运行单个实验"""
    print(f"开始实验: {exp_name}")
    print(f"配置: {config}")
    
    # 记录开始时间
    start_time = time.time()
    
    # 这里添加你的训练命令
    # 例如：python run_train_slotGAT_on_all_dataset.py
    cmd = "python your_training_script.py"  # 替换为你的训练脚本
    
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        success = result.returncode == 0
        
        # 记录结果
        end_time = time.time()
        duration = end_time - start_time
        
        experiment_result = {
            'name': exp_name,
            'config': config,
            'start_time': datetime.fromtimestamp(start_time).isoformat(),
            'duration': duration,
            'success': success,
            'stdout': result.stdout,
            'stderr': result.stderr
        }
        
        # 保存结果
        result_file = f"/root/experiments/results/{exp_name}_{int(start_time)}.json"
        with open(result_file, 'w') as f:
            json.dump(experiment_result, f, indent=2)
        
        print(f"实验完成: {exp_name}, 耗时: {duration:.2f}秒")
        return experiment_result
        
    except Exception as e:
        print(f"实验失败: {exp_name}, 错误: {str(e)}")
        return None

if __name__ == "__main__":
    print("AutoDL实验管理器")
    print("请根据需要修改此脚本")
EOF

chmod +x /root/experiments/run_experiment.py

echo "=== AutoDL环境配置完成 ==="
echo ""
echo "使用说明："
echo "1. 运行 'bash /root/monitor_gpu.sh' 监控GPU"
echo "2. 工作目录: /root/experiments"
echo "3. 日志目录: /root/experiments/logs"
echo "4. 结果目录: /root/experiments/results"
echo ""
echo "下一步："
echo "1. 上传你的代码到 /root/experiments"
echo "2. 修改 run_experiment.py 中的训练命令"
echo "3. 开始实验"
