# AutoDL Transformer超参数实验指南

## 1. AutoDL平台准备

### 1.1 注册和充值
1. 注册AutoDL账号：https://www.autodl.com/
2. 充值建议：先充值100-200元测试
3. 实名认证（必需）

### 1.2 创建实例
1. **选择镜像**：
   - PyTorch 1.11.0
   - Python 3.8
   - CUDA 11.3

2. **选择GPU**：
   - **推荐**：RTX 3080 (10GB显存，~2.5元/小时)
   - **备选**：RTX 4090 (24GB显存，~4.5元/小时)

3. **存储配置**：
   - 系统盘：50GB（免费）
   - 数据盘：根据需要选择

## 2. 环境配置步骤

### 2.1 连接到实例
```bash
# 使用AutoDL提供的SSH命令连接
ssh -p [端口] root@[IP地址]
```

### 2.2 运行环境配置脚本
```bash
# 上传并运行配置脚本
bash autodl_setup.sh
```

### 2.3 上传你的代码
```bash
# 方法1：使用scp上传
scp -P [端口] -r /path/to/your/code root@[IP地址]:/root/experiments/

# 方法2：使用git克隆
cd /root/experiments
git clone [你的代码仓库]
```

## 3. 实验运行流程

### 3.1 准备工作
1. **检查代码结构**：
   ```
   /root/experiments/
   ├── SlotGAT_ICML23-new/
   ├── autodl_experiment_runner.py
   ├── 显存监控工具.py
   └── logs/
   ```

2. **修改训练脚本路径**：
   在 `autodl_experiment_runner.py` 中修改：
   ```python
   original_script = "/root/experiments/SlotGAT_ICML23-new/NC/methods/SlotGAT/run_train_slotGAT_on_all_dataset.py"
   ```

### 3.2 运行实验
```bash
cd /root/experiments
python autodl_experiment_runner.py
```

### 3.3 监控实验进度
```bash
# 新开一个终端窗口
bash /root/monitor_gpu.sh

# 或者查看实时日志
tail -f /root/experiments/logs/[实验名称].log
```

## 4. 实验配置说明

### 4.1 自动生成的实验配置
脚本会自动生成以下实验：

**阶段1：基线对比**
- `baseline_no_residual`: 无残差连接
- `baseline_with_residual`: 标准残差连接

**阶段2：注意力头数调优**
- `nhead_1`, `nhead_2`, `nhead_4`, `nhead_8`, `nhead_16`, `nhead_32`

**阶段3：层数调优**
- `layers_1`, `layers_2`, `layers_3`, `layers_4`, `layers_6`

### 4.2 手动配置实验
如果需要自定义实验，修改 `autodl_experiment_runner.py` 中的 `generate_experiment_configs()` 函数。

## 5. 成本控制

### 5.1 预估成本
| 实验阶段 | 实验数量 | 单次时间 | 总时间 | 成本(RTX 3080) |
|----------|----------|----------|--------|----------------|
| 基线测试 | 2个 | 30分钟 | 1小时 | ~2.5元 |
| 注意力头数 | 6个 | 30分钟 | 3小时 | ~7.5元 |
| 层数调优 | 5个 | 30分钟 | 2.5小时 | ~6.25元 |
| **总计** | **13个** | - | **6.5小时** | **~16.25元** |

### 5.2 节省成本的技巧
1. **先用小数据集测试**：确认代码无误
2. **设置实验超时**：避免卡死浪费时间
3. **及时关机**：实验完成后立即关闭实例
4. **使用快照**：保存配置好的环境

## 6. 常见问题和解决方案

### 6.1 显存不足
```bash
# 检查显存使用
nvidia-smi

# 运行显存诊断工具
python 显存监控工具.py
```

**解决方案**：
- 减少batch size
- 使用梯度检查点
- 升级到更大显存的GPU

### 6.2 网络连接问题
```bash
# 使用国内镜像源
pip install -i https://pypi.tuna.tsinghua.edu.cn/simple/ [包名]
```

### 6.3 实验中断
- 所有结果都保存在 `/root/experiments/results/`
- 可以从中断的地方继续运行

### 6.4 数据传输
```bash
# 下载结果到本地
scp -P [端口] -r root@[IP地址]:/root/experiments/results/ ./
```

## 7. 实验结果分析

### 7.1 查看实验总结
```bash
cd /root/experiments/results
ls *.csv  # 查看生成的总结文件
```

### 7.2 结果文件说明
- `experiment_summary_[时间戳].csv`: 所有实验的汇总
- `[实验名称]_[时间戳].json`: 单个实验的详细结果
- `/logs/[实验名称].log`: 训练日志

## 8. 最佳实践

### 8.1 实验前检查清单
- [ ] GPU状态正常
- [ ] 代码路径正确
- [ ] 数据集已上传
- [ ] 依赖包已安装
- [ ] 有足够的存储空间

### 8.2 实验中监控
- [ ] 定期检查GPU使用率
- [ ] 监控实验进度
- [ ] 检查日志输出
- [ ] 关注余额变化

### 8.3 实验后处理
- [ ] 下载重要结果
- [ ] 分析实验数据
- [ ] 清理临时文件
- [ ] 关闭实例

## 9. 紧急情况处理

### 9.1 实例卡死
```bash
# 强制重启实例（在AutoDL控制台操作）
# 或者联系客服
```

### 9.2 余额不足
- 及时充值
- 设置余额预警

### 9.3 数据丢失
- 重要数据及时备份
- 使用快照功能

## 10. 联系支持

- **AutoDL客服**：平台内工单系统
- **技术问题**：查看AutoDL文档和社区
- **代码问题**：检查日志文件

---

## 快速开始命令

```bash
# 1. 连接实例
ssh -p [端口] root@[IP地址]

# 2. 配置环境
bash autodl_setup.sh

# 3. 上传代码
# (使用scp或git)

# 4. 运行实验
cd /root/experiments
python autodl_experiment_runner.py

# 5. 监控进度
bash /root/monitor_gpu.sh
```

记住：**实验完成后及时关闭实例以节省费用！**
