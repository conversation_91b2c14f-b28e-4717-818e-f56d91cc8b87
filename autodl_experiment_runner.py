"""
AutoDL专用的Transformer超参数实验运行器
"""

import os
import json
import time
import subprocess
import pandas as pd
from datetime import datetime
import torch

class AutoDLExperimentRunner:
    """AutoDL实验运行器"""
    
    def __init__(self, base_dir="/root/experiments"):
        self.base_dir = base_dir
        self.results_dir = os.path.join(base_dir, "results")
        self.logs_dir = os.path.join(base_dir, "logs")
        self.configs_dir = os.path.join(base_dir, "configs")
        
        # 创建目录
        os.makedirs(self.results_dir, exist_ok=True)
        os.makedirs(self.logs_dir, exist_ok=True)
        os.makedirs(self.configs_dir, exist_ok=True)
        
        self.results = []
    
    def check_environment(self):
        """检查AutoDL环境"""
        print("=== AutoDL环境检查 ===")
        
        # 检查GPU
        if torch.cuda.is_available():
            gpu_name = torch.cuda.get_device_name(0)
            gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
            print(f"✅ GPU: {gpu_name}")
            print(f"✅ GPU内存: {gpu_memory:.1f}GB")
        else:
            print("❌ GPU不可用")
            return False
        
        # 检查磁盘空间
        disk_usage = subprocess.run(['df', '-h', '/'], capture_output=True, text=True)
        print(f"✅ 磁盘使用情况:")
        print(disk_usage.stdout.split('\n')[1])
        
        # 检查Python包
        try:
            import dgl
            import torch
            print(f"✅ PyTorch: {torch.__version__}")
            print(f"✅ DGL: {dgl.__version__}")
        except ImportError as e:
            print(f"❌ 缺少依赖: {e}")
            return False
        
        return True
    
    def generate_experiment_configs(self):
        """生成实验配置"""
        configs = []
        
        # 基线实验
        configs.extend([
            {
                'name': 'baseline_no_residual',
                'transformer_config': {'d_model': 512, 'nhead': 8, 'num_layers': 2},
                'residual_strategy': 'none',
                'priority': 1
            },
            {
                'name': 'baseline_with_residual', 
                'transformer_config': {'d_model': 512, 'nhead': 8, 'num_layers': 2},
                'residual_strategy': 'add',
                'priority': 1
            }
        ])
        
        # 注意力头数实验
        for nhead in [1, 2, 4, 8, 16, 32]:
            if 512 % nhead == 0:  # 确保能整除
                configs.append({
                    'name': f'nhead_{nhead}',
                    'transformer_config': {'d_model': 512, 'nhead': nhead, 'num_layers': 2},
                    'residual_strategy': 'add',
                    'priority': 2
                })
        
        # 层数实验
        for num_layers in [1, 2, 3, 4, 6]:
            configs.append({
                'name': f'layers_{num_layers}',
                'transformer_config': {'d_model': 512, 'nhead': 8, 'num_layers': num_layers},
                'residual_strategy': 'add', 
                'priority': 3
            })
        
        # 保存配置
        config_file = os.path.join(self.configs_dir, 'experiment_configs.json')
        with open(config_file, 'w') as f:
            json.dump(configs, f, indent=2)
        
        print(f"生成了 {len(configs)} 个实验配置")
        return configs
    
    def modify_code_for_config(self, config, temp_script_path):
        """修改代码以使用指定配置"""
        # 这里需要根据你的具体代码结构来修改
        # 假设你的训练脚本是 run_train_slotGAT_on_all_dataset.py
        
        # 读取原始脚本（你需要指定正确的路径）
        original_script = "run_train_slotGAT_on_all_dataset.py"
        
        if not os.path.exists(original_script):
            print(f"警告: 找不到训练脚本 {original_script}")
            return False
        
        with open(original_script, 'r') as f:
            content = f.read()
        
        # 在脚本开头插入配置
        config_code = f"""
# AutoDL自动生成的实验配置
import sys
sys.path.append('/root/experiments')

# Transformer配置
TRANSFORMER_CONFIG = {config['transformer_config']}
RESIDUAL_STRATEGY = '{config['residual_strategy']}'

print(f"实验配置: {{TRANSFORMER_CONFIG}}")
print(f"残差策略: {{RESIDUAL_STRATEGY}}")

"""
        
        # 修改后的内容
        modified_content = config_code + content
        
        # 保存临时脚本
        with open(temp_script_path, 'w') as f:
            f.write(modified_content)
        
        return True
    
    def run_single_experiment(self, config):
        """运行单个实验"""
        exp_name = config['name']
        print(f"\n{'='*50}")
        print(f"开始实验: {exp_name}")
        print(f"配置: {config}")
        print(f"{'='*50}")
        
        # 记录开始时间
        start_time = time.time()
        
        # 创建临时脚本
        temp_script = os.path.join(self.base_dir, f"temp_{exp_name}.py")
        
        if not self.modify_code_for_config(config, temp_script):
            return None
        
        # 创建日志文件
        log_file = os.path.join(self.logs_dir, f"{exp_name}_{int(start_time)}.log")
        
        # 运行实验
        cmd = f"cd {self.base_dir} && python {temp_script} > {log_file} 2>&1"
        
        try:
            print(f"执行命令: {cmd}")
            result = subprocess.run(cmd, shell=True, timeout=3600)  # 1小时超时
            
            end_time = time.time()
            duration = end_time - start_time
            success = result.returncode == 0
            
            # 解析结果（你需要根据实际输出格式调整）
            metrics = self.parse_log_file(log_file)
            
            experiment_result = {
                'name': exp_name,
                'config': config,
                'start_time': datetime.fromtimestamp(start_time).isoformat(),
                'end_time': datetime.fromtimestamp(end_time).isoformat(),
                'duration': duration,
                'success': success,
                'metrics': metrics,
                'log_file': log_file
            }
            
            # 保存结果
            result_file = os.path.join(self.results_dir, f"{exp_name}_{int(start_time)}.json")
            with open(result_file, 'w') as f:
                json.dump(experiment_result, f, indent=2)
            
            self.results.append(experiment_result)
            
            # 清理临时文件
            if os.path.exists(temp_script):
                os.remove(temp_script)
            
            print(f"✅ 实验完成: {exp_name}")
            print(f"   耗时: {duration:.2f}秒")
            print(f"   成功: {success}")
            if metrics:
                print(f"   指标: {metrics}")
            
            return experiment_result
            
        except subprocess.TimeoutExpired:
            print(f"❌ 实验超时: {exp_name}")
            return None
        except Exception as e:
            print(f"❌ 实验失败: {exp_name}, 错误: {str(e)}")
            return None
    
    def parse_log_file(self, log_file):
        """解析日志文件获取指标"""
        metrics = {}
        
        if not os.path.exists(log_file):
            return metrics
        
        try:
            with open(log_file, 'r') as f:
                content = f.read()
            
            # 这里需要根据你的实际日志格式来解析
            # 例如查找准确率、F1分数等
            import re
            
            # 查找准确率
            acc_match = re.search(r'accuracy[:\s]+([0-9.]+)', content, re.IGNORECASE)
            if acc_match:
                metrics['accuracy'] = float(acc_match.group(1))
            
            # 查找F1分数
            f1_match = re.search(r'f1[:\s]+([0-9.]+)', content, re.IGNORECASE)
            if f1_match:
                metrics['f1'] = float(f1_match.group(1))
            
            # 查找损失
            loss_match = re.search(r'loss[:\s]+([0-9.]+)', content, re.IGNORECASE)
            if loss_match:
                metrics['loss'] = float(loss_match.group(1))
                
        except Exception as e:
            print(f"解析日志文件失败: {e}")
        
        return metrics
    
    def run_experiments(self, configs, max_experiments=None):
        """运行多个实验"""
        if max_experiments:
            configs = configs[:max_experiments]
        
        print(f"准备运行 {len(configs)} 个实验")
        
        # 按优先级排序
        configs.sort(key=lambda x: x.get('priority', 999))
        
        for i, config in enumerate(configs):
            print(f"\n进度: {i+1}/{len(configs)}")
            
            try:
                self.run_single_experiment(config)
            except KeyboardInterrupt:
                print("用户中断实验")
                break
            except Exception as e:
                print(f"实验异常: {e}")
                continue
        
        # 生成总结报告
        self.generate_summary_report()
    
    def generate_summary_report(self):
        """生成实验总结报告"""
        if not self.results:
            print("没有实验结果")
            return
        
        # 创建DataFrame
        df_data = []
        for result in self.results:
            row = {
                'name': result['name'],
                'success': result['success'],
                'duration': result['duration'],
                **result.get('metrics', {})
            }
            # 添加配置信息
            if 'transformer_config' in result['config']:
                row.update({f"tf_{k}": v for k, v in result['config']['transformer_config'].items()})
            row['residual'] = result['config'].get('residual_strategy', 'unknown')
            
            df_data.append(row)
        
        df = pd.DataFrame(df_data)
        
        # 保存CSV
        csv_file = os.path.join(self.results_dir, f"experiment_summary_{int(time.time())}.csv")
        df.to_csv(csv_file, index=False)
        
        # 打印总结
        print(f"\n{'='*60}")
        print("实验总结报告")
        print(f"{'='*60}")
        print(f"总实验数: {len(df)}")
        print(f"成功实验数: {df['success'].sum()}")
        print(f"平均耗时: {df['duration'].mean():.2f}秒")
        
        if 'accuracy' in df.columns:
            successful_df = df[df['success'] == True]
            if len(successful_df) > 0:
                best_exp = successful_df.loc[successful_df['accuracy'].idxmax()]
                print(f"最佳准确率: {best_exp['accuracy']:.4f} (实验: {best_exp['name']})")
        
        print(f"详细结果保存在: {csv_file}")

def main():
    """主函数"""
    runner = AutoDLExperimentRunner()
    
    # 检查环境
    if not runner.check_environment():
        print("环境检查失败，请先配置环境")
        return
    
    # 生成实验配置
    configs = runner.generate_experiment_configs()
    
    # 运行实验
    print("\n开始运行实验...")
    print("提示: 可以随时按 Ctrl+C 中断实验")
    
    try:
        runner.run_experiments(configs, max_experiments=10)  # 限制实验数量
    except KeyboardInterrupt:
        print("\n实验被用户中断")
    
    print("\n所有实验完成！")

if __name__ == "__main__":
    main()
